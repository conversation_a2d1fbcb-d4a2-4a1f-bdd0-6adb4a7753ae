import { Component, Input, OnInit } from '@angular/core';
import { AccountService } from 'src/app/modules/account/services/account.service';
import { ModalService } from 'src/app/shared/services/modal.service';
import { forkJoin } from 'rxjs';
import { EMPTY_GUID } from '../../constant';

@Component({
    selector: 'app-service-activities',
    templateUrl: './service-activities.component.html',
    styleUrls: ['./service-activities.component.scss']
})
export class ServiceActivitiesComponent implements OnInit {
    @Input() userId: string = '';
    @Input() userProfilePhoto: string = '';
    @Input() userName: string = '';
    @Input() userRole: string = '';
    @Input() isLoggedIn: boolean = false;
    @Input() currentUserId: string = '';
    @Input() currentUserPhoto: string = '';
    @Input() currentUserName: string = '';

    serviceActivities: any[] = [];
    serviceActivitiesLoading = false;
    commentInputs: { [activityId: string]: string } = {};
    showAllComments: { [activityId: string]: boolean } = {};
    likeLoadingStates: { [activityId: string]: boolean } = {};
    errorMessages: { [activityId: string]: string } = {};

    constructor(
        private accountService: AccountService,
        private modalService: ModalService
    ) { }

    ngOnInit(): void {
        if (this.userId) {
            this.getServiceActivities();
        }
    }

    getServiceActivities() {
        this.serviceActivitiesLoading = true;
        this.accountService.getServiceActivities(this.userId).subscribe({
            next: (response: any) => {
                this.serviceActivities = (response || []).sort((a: any, b: any) =>
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                );
                this.serviceActivities.forEach((activity: any) => {
                    // These should be set by backend for each activity:
                    // activity.isLiked = true/false (if current user liked)
                    // activity.likeCount = number of likes
                    // If not, set defaults:
                    if (activity.likeCount === undefined || activity.likeCount === null) activity.likeCount = 0;
                    if (activity.isLiked === undefined || activity.isLiked === null) activity.isLiked = false;
                    if (activity.likeId === undefined) activity.likeId = null;
                });
                this.loadCommentsAndLikesForActivities();
            },
            error: (_error) => {
                this.serviceActivities = [];
                this.serviceActivitiesLoading = false;
            }
        });
    }

    loadCommentsAndLikesForActivities() {
        if (this.serviceActivities.length === 0) {
            this.serviceActivitiesLoading = false;
            return;
        }

        const commentRequests = this.serviceActivities.map(activity =>
            this.accountService.getActivityComments(activity.id)
        );

        const likeRequests = this.serviceActivities.map(activity =>
            this.accountService.getPostLikes(activity.id)
        );

        forkJoin([
            forkJoin(commentRequests),
            forkJoin(likeRequests)
        ]).subscribe({
            next: ([commentsResponses, likesResponses]: [any[], any[]]) => {
                this.serviceActivities.forEach((activity, index) => {
                    // Handle comments
                    activity.comments = commentsResponses[index] || [];
                    activity.commentCount = activity.comments.length;

                    // Handle likes
                    const likesData = likesResponses[index];
                    this.processLikesData(activity, likesData);

                    // Process comment user data
                    activity.comments.forEach((comment: any) => {
                        if (comment.userId === this.currentUserId) {
                            if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                comment.userProfilePhoto = this.currentUserPhoto;
                            }
                            if (!comment.userName && this.currentUserName) {
                                comment.userName = this.currentUserName;
                            }
                        }
                    });

                    activity.comments.sort((a: any, b: any) =>
                        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                    );
                });
                this.serviceActivitiesLoading = false;
            },
            error: (error) => {
                console.error('Error loading comments and likes:', error);
                this.serviceActivities.forEach((activity: any) => {
                    if (!activity.comments) activity.comments = [];
                    if (activity.likeCount === undefined) activity.likeCount = 0;
                    if (activity.isLiked === undefined) activity.isLiked = false;
                    if (activity.likeId === undefined) activity.likeId = null;
                });
                this.serviceActivitiesLoading = false;
            }
        });
    }

    private processLikesData(activity: any, likesData: any) {
        if (likesData && typeof likesData === 'object') {
            if (Array.isArray(likesData)) {
                // Response is an array of likes
                activity.likeCount = likesData.length;
                if (this.currentUserId && this.currentUserId.trim() !== '') {
                    const userLike = likesData.find((like: any) =>
                        like.userId === this.currentUserId || like.user?.id === this.currentUserId
                    );
                    activity.isLiked = !!userLike;
                    activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                } else {
                    activity.isLiked = false;
                    activity.likeId = null;
                }
            } else if (likesData.data && Array.isArray(likesData.data)) {
                // Response has data property with array
                activity.likeCount = likesData.data.length;
                if (this.currentUserId && this.currentUserId.trim() !== '') {
                    const userLike = likesData.data.find((like: any) =>
                        like.userId === this.currentUserId || like.user?.id === this.currentUserId
                    );
                    activity.isLiked = !!userLike;
                    activity.likeId = userLike ? (userLike.id || userLike.likeId) : null;
                } else {
                    activity.isLiked = false;
                    activity.likeId = null;
                }
            } else {
                // Response is an object with count properties
                activity.likeCount = likesData.likeCount || likesData.count || likesData.totalLikes || 0;
                activity.isLiked = likesData.isLiked || likesData.likedByCurrentUser || likesData.userLiked || false;
                activity.likeId = likesData.likeId || likesData.id || null;
            }
        } else {
            // Default values if no valid response
            activity.likeCount = 0;
            activity.isLiked = false;
            activity.likeId = null;
        }
    }

    toggleLike(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId || this.likeLoadingStates[activity.id]) {
            return;
        }

        // Clear any previous error messages
        this.errorMessages[activity.id] = '';

        // Set loading state
        this.likeLoadingStates[activity.id] = true;

        // Store original values for rollback
        const originalIsLiked = activity.isLiked;
        const originalLikeCount = activity.likeCount;
        const originalLikeId = activity.likeId;

        // Optimistic update
        activity.isLiked = !activity.isLiked;
        if (activity.isLiked) {
            activity.likeCount = (activity.likeCount || 0) + 1;
            // Call likePost
            this.accountService.likePost(activity.id).subscribe({
                next: (response: any) => {
                    // Success - keep optimistic update
                    this.likeLoadingStates[activity.id] = false;
                    // Optionally refresh like data from server response
                    if (response && response.likeCount !== undefined) {
                        activity.likeCount = response.likeCount;
                    }
                    if (response && response.likeId) {
                        activity.likeId = response.likeId;
                    }
                },
                error: (error) => {
                    // Revert optimistic update
                    activity.isLiked = originalIsLiked;
                    activity.likeCount = originalLikeCount;
                    activity.likeId = originalLikeId;
                    this.likeLoadingStates[activity.id] = false;
                    this.errorMessages[activity.id] = 'Failed to like post. Please try again.';
                    console.error('Error liking post:', error);

                    // Clear error message after 3 seconds
                    setTimeout(() => {
                        this.errorMessages[activity.id] = '';
                    }, 3000);
                }
            });
        } else {
            activity.likeCount = Math.max((activity.likeCount || 1) - 1, 0);
            activity.likeId = null;
            // Call unlikePost
            this.accountService.unlikePost(activity.id).subscribe({
                next: (response: any) => {
                    // Success - keep optimistic update
                    this.likeLoadingStates[activity.id] = false;
                    // Optionally refresh like data from server response
                    if (response && response.likeCount !== undefined) {
                        activity.likeCount = response.likeCount;
                    }
                },
                error: (error) => {
                    // Revert optimistic update
                    activity.isLiked = originalIsLiked;
                    activity.likeCount = originalLikeCount;
                    activity.likeId = originalLikeId;
                    this.likeLoadingStates[activity.id] = false;
                    this.errorMessages[activity.id] = 'Failed to unlike post. Please try again.';
                    console.error('Error unliking post:', error);

                    // Clear error message after 3 seconds
                    setTimeout(() => {
                        this.errorMessages[activity.id] = '';
                    }, 3000);
                }
            });
        }
    }

    clearErrorMessage(activityId: string) {
        this.errorMessages[activityId] = '';
    }

    postComment(activity: any) {
        if (!this.isLoggedIn || !this.currentUserId || !this.commentInputs[activity.id]?.trim()) {
            return;
        }

        const payload = {
            activityId: activity.id,
            userId: this.currentUserId,
            content: this.commentInputs[activity.id]
        };



        const commentText = this.commentInputs[activity.id];

        this.accountService.addActivityComment(payload).subscribe({
            next: (_response: any) => {
                this.accountService.getActivityComments(activity.id).subscribe({
                    next: (commentsResponse: any[]) => {

                        activity.comments = commentsResponse || [];
                        activity.commentCount = activity.comments.length;

                        activity.comments.forEach((comment: any) => {
                            if (comment.userId === this.currentUserId) {
                                if (!comment.userProfilePhoto && this.currentUserPhoto) {
                                    comment.userProfilePhoto = this.currentUserPhoto;
                                }
                                if (!comment.userName && this.currentUserName) {
                                    comment.userName = this.currentUserName;
                                }
                            }
                        });
                        activity.comments.sort((a: any, b: any) =>
                            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                        );
                        this.commentInputs[activity.id] = '';
                    },
                    error: (_error) => {
                        const newComment = {
                            userId: this.currentUserId,
                            content: commentText,
                            createdAt: new Date(),
                            userName: this.currentUserName || this.userName || 'User',
                            userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                        };
                        activity.comments = activity.comments || [];
                        activity.comments.unshift(newComment);
                        activity.commentCount = activity.comments.length;
                        this.commentInputs[activity.id] = '';
                    }
                });
            },
            error: (_error) => {
                const newComment = {
                    userId: this.currentUserId,
                    content: commentText,
                    createdAt: new Date(),
                    userName: this.currentUserName || this.userName || 'User',
                    userProfilePhoto: this.currentUserPhoto || this.userProfilePhoto || './assets/images/default-avatar.png'
                };
                activity.comments = activity.comments || [];
                activity.comments.unshift(newComment);
                this.commentInputs[activity.id] = '';
            }
        });
    }

    onCommentKeyDown(event: KeyboardEvent, activity: any) {
        if (event.key === 'Enter' && this.commentInputs[activity.id]?.trim()) {
            this.postComment(activity);
        }
    }

    createPost() {
        if (!this.isLoggedIn || !this.currentUserId) {
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: false,
                postData: null,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        const modalRef = this.modalService.openModal('create-edit-post', modalData);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    editPost(activity: any) {
        if (!this.canEditPost(activity)) {
            return;
        }

        const modalData = {
            initialState: {
                isEditMode: true,
                postData: activity,
                userId: this.currentUserId,
                userProfilePhoto: this.userProfilePhoto,
                userName: this.userName
            }
        };

        const modalRef = this.modalService.openModal('create-edit-post', modalData);

        if (modalRef) {
            modalRef.content?.onClose?.subscribe((result: any) => {
                if (result && result.success) {
                    this.getServiceActivities();
                }
            });
        }
    }

    canEditPost(activity: any): boolean {

        if (activity.userId) {
            return !!(this.isLoggedIn && this.currentUserId &&
                activity.userId.toLowerCase() === this.currentUserId.toLowerCase());
        }
        const isOwnProfile = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());
        return isOwnProfile;
    }

    isOwnProfile(): boolean {
        const isOwn = !!(this.isLoggedIn && this.currentUserId && this.userId &&
            this.userId.toLowerCase() === this.currentUserId.toLowerCase());
        return isOwn;
    }

    getDisplayName(activity: any): string {
        // Try activity data first
        if (activity.userName && activity.userName.trim()) {
            return activity.userName;
        }

        // Try firstName + lastName from activity
        if (activity.firstName || activity.lastName) {
            const firstName = activity.firstName || '';
            const lastName = activity.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        // Fallback to input parameters (profile data)
        return this.userName || 'User';
    }

    getDisplayRole(activity: any): string {
        // Try activity data first
        if (activity.userRole && activity.userRole.trim()) {
            return activity.userRole;
        }

        if (activity.roleName && activity.roleName.trim()) {
            return activity.roleName;
        }

        return this.userRole || '';
    }

    getDisplayPhoto(activity: any): string {
        // Try activity data first
        if (activity.userProfilePhoto && activity.userProfilePhoto.trim()) {
            return activity.userProfilePhoto;
        }

        if (activity.profilePhoto && activity.profilePhoto.trim()) {
            return activity.profilePhoto;
        }

        return this.userProfilePhoto || './assets/images/default-avatar.png';
    }

    getCommentDisplayName(comment: any): string {
        // Try comment data first
        if (comment.userName && comment.userName.trim()) {
            return comment.userName;
        }

        // Try firstName + lastName from comment
        if (comment.firstName || comment.lastName) {
            const firstName = comment.firstName || '';
            const lastName = comment.lastName || '';
            const fullName = `${firstName} ${lastName}`.trim();
            if (fullName) {
                return fullName;
            }
        }

        if (comment.userId === this.currentUserId) {
            if (this.currentUserName && this.currentUserName.trim()) {
                return this.currentUserName;
            }
            // Fallback to profile name if current user name not available
            if (this.userName && this.userName.trim()) {
                return this.userName;
            }
        }

        // Default fallback
        return 'User';
    }

    getCommentDisplayPhoto(comment: any): string {
        if (comment.userProfilePhoto && comment.userProfilePhoto.trim()) {
            return comment.userProfilePhoto;
        }

        if (comment.profilePhoto && comment.profilePhoto.trim()) {
            return comment.profilePhoto;
        }

        if (comment.userId === this.currentUserId) {
            if (this.currentUserPhoto && this.currentUserPhoto.trim()) {
                return this.currentUserPhoto;
            }
            if (this.userProfilePhoto && this.userProfilePhoto.trim()) {
                return this.userProfilePhoto;
            }
        }
        return './assets/images/default-avatar.png';
    }

    getDisplayedComments(activity: any): any[] {
        if (!activity.comments) return [];

        const shouldShowAll = this.showAllComments[activity.id];
        return shouldShowAll ? activity.comments : activity.comments.slice(0, 3);
    }

    shouldShowViewAllLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && !this.showAllComments[activity.id];
    }

    shouldShowViewLessLink(activity: any): boolean {
        return activity.comments && activity.comments.length > 3 && this.showAllComments[activity.id];
    }

    toggleShowAllComments(activity: any): void {
        this.showAllComments[activity.id] = !this.showAllComments[activity.id];
    }


}